#!/usr/bin/env python3
"""
测试Robust TableQA项目的导入
"""

import sys
import os
from pathlib import Path

def setup_paths():
    """设置Python路径"""
    current_dir = Path.cwd()
    src_dir = current_dir / "src"
    colbert_dir = src_dir / "ColBERT"
    
    # 添加到sys.path
    paths_to_add = [str(src_dir), str(colbert_dir)]
    for path in paths_to_add:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    print(f"📁 添加路径到PYTHONPATH:")
    for path in paths_to_add:
        print(f"  - {path}")

def test_basic_imports():
    """测试基础导入"""
    print("\n🧪 测试基础导入...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        # 测试MPS
        if torch.backends.mps.is_available():
            print("✅ MPS GPU加速可用")
        else:
            print("⚠️  MPS不可用，使用CPU")
            
    except ImportError as e:
        print(f"❌ PyTorch导入失败: {e}")
        return False
    
    try:
        import pytorch_lightning as pl
        print(f"✅ PyTorch Lightning: {pl.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch Lightning导入失败: {e}")
        return False
    
    return True

def test_colbert_import():
    """测试ColBERT导入"""
    print("\n🔍 测试ColBERT导入...")
    
    try:
        import colbert
        print("✅ ColBERT基础模块导入成功")
    except ImportError as e:
        print(f"❌ ColBERT基础模块导入失败: {e}")
        return False
    
    try:
        from colbert.modeling.tokenization import QueryTokenizer, DocTokenizer
        print("✅ ColBERT tokenization导入成功")
    except ImportError as e:
        print(f"❌ ColBERT tokenization导入失败: {e}")
        return False
    
    try:
        from colbert.modeling.tokenization import tensorize_triples
        print("✅ ColBERT tensorize_triples导入成功")
    except ImportError as e:
        print(f"❌ ColBERT tensorize_triples导入失败: {e}")
        return False
    
    return True

def test_project_imports():
    """测试项目模块导入"""
    print("\n📦 测试项目模块导入...")
    
    try:
        from data_loader_manager import *
        print("✅ data_loader_manager导入成功")
    except ImportError as e:
        print(f"❌ data_loader_manager导入失败: {e}")
        print("详细错误信息:")
        import traceback
        traceback.print_exc()
        return False
    
    try:
        import trainers
        print("✅ trainers模块导入成功")
    except ImportError as e:
        print(f"❌ trainers模块导入失败: {e}")
        return False
    
    try:
        import models
        print("✅ models模块导入成功")
    except ImportError as e:
        print(f"❌ models模块导入失败: {e}")
        return False
    
    return True

def test_main_script():
    """测试主脚本"""
    print("\n🚀 测试主脚本...")
    
    main_script = Path("src/main.py")
    if not main_script.exists():
        print(f"❌ 主脚本不存在: {main_script}")
        return False
    
    try:
        # 尝试导入main模块（不执行）
        import importlib.util
        spec = importlib.util.spec_from_file_location("main", main_script)
        main_module = importlib.util.module_from_spec(spec)
        
        # 这里不执行spec.loader.exec_module(main_module)
        # 因为可能会尝试运行训练
        
        print("✅ 主脚本可以被导入")
        return True
        
    except Exception as e:
        print(f"❌ 主脚本导入失败: {e}")
        return False

def check_dependencies():
    """检查关键依赖"""
    print("\n📋 检查关键依赖...")
    
    dependencies = [
        "torch", "pytorch_lightning", "transformers", "datasets",
        "jsonnet", "easydict", "tqdm", "wandb", "tensorboard",
        "numpy", "scipy", "sklearn"
    ]
    
    missing_deps = []
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            print(f"❌ {dep} (缺失)")
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"\n⚠️  缺失依赖: {', '.join(missing_deps)}")
        print("请运行: pip install " + " ".join(missing_deps))
        return False
    
    return True

def provide_solutions():
    """提供解决方案"""
    print("\n🔧 解决方案:")
    print("=" * 50)
    
    print("1. 安装ColBERT:")
    print("   cd src/ColBERT")
    print("   pip install -e .")
    print("   cd ../..")
    
    print("\n2. 设置环境变量:")
    print("   export PYTHONPATH=\"$PYTHONPATH:$(pwd)/src:$(pwd)/src/ColBERT\"")
    
    print("\n3. 安装缺失依赖:")
    print("   pip install pytorch-lightning==1.8.2 transformers==4.22.1")
    print("   pip install datasets jsonnet easydict tqdm wandb tensorboard")
    
    print("\n4. 运行自动化脚本:")
    print("   python setup_robust_tableqa_m4.py")
    
    print("\n5. 创建运行脚本:")
    print("   echo '#!/bin/bash' > run.sh")
    print("   echo 'export PYTHONPATH=\"$PYTHONPATH:$(pwd)/src:$(pwd)/src/ColBERT\"' >> run.sh")
    print("   echo 'python src/main.py \"$@\"' >> run.sh")
    print("   chmod +x run.sh")

def main():
    """主函数"""
    print("🍎 Robust TableQA Mac M4 导入测试")
    print("=" * 60)
    
    # 检查是否在正确目录
    if not Path("src/main.py").exists():
        print("❌ 请在项目根目录运行此脚本")
        print("当前目录应包含 src/main.py 文件")
        return False
    
    # 设置路径
    setup_paths()
    
    # 运行测试
    tests = [
        ("基础导入", test_basic_imports),
        ("依赖检查", check_dependencies),
        ("ColBERT导入", test_colbert_import),
        ("项目模块导入", test_project_imports),
        ("主脚本测试", test_main_script),
    ]
    
    passed_tests = []
    failed_tests = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed_tests.append(test_name)
                print(f"✅ {test_name} 通过")
            else:
                failed_tests.append(test_name)
                print(f"❌ {test_name} 失败")
        except Exception as e:
            failed_tests.append(test_name)
            print(f"💥 {test_name} 出现异常: {e}")
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"✅ 通过: {len(passed_tests)}")
    print(f"❌ 失败: {len(failed_tests)}")
    
    if passed_tests:
        print(f"\n通过的测试: {', '.join(passed_tests)}")
    
    if failed_tests:
        print(f"\n失败的测试: {', '.join(failed_tests)}")
        provide_solutions()
        return False
    else:
        print("\n🎉 所有测试通过！环境配置正确。")
        print("✅ 可以运行项目了")
        print("\n使用方法:")
        print("  python src/main.py --help")
        return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
